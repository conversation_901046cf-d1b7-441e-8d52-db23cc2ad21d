<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')"/>
    <PropertyGroup>
        <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
        <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
        <ProjectGuid>{7ABED234-784C-4514-AACA-423DC0520092}</ProjectGuid>
        <OutputType>WinExe</OutputType>
        <RootNamespace>AutoPlus</RootNamespace>
        <AssemblyName>AutoPlus</AssemblyName>
        <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
        <FileAlignment>512</FileAlignment>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
        <Deterministic>true</Deterministic>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
        <PlatformTarget>AnyCPU</PlatformTarget>
        <DebugSymbols>true</DebugSymbols>
        <DebugType>full</DebugType>
        <Optimize>false</Optimize>
        <OutputPath>bin\Debug\</OutputPath>
        <DefineConstants>DEBUG;TRACE</DefineConstants>
        <ErrorReport>prompt</ErrorReport>
        <WarningLevel>4</WarningLevel>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
        <PlatformTarget>AnyCPU</PlatformTarget>
        <DebugType>pdbonly</DebugType>
        <Optimize>true</Optimize>
        <OutputPath>bin\Release\</OutputPath>
        <DefineConstants>TRACE</DefineConstants>
        <ErrorReport>prompt</ErrorReport>
        <WarningLevel>4</WarningLevel>
    </PropertyGroup>
    <ItemGroup>
        <Reference Include="Bunifu.Licensing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=f89b4760ba7dcb6b, processorArchitecture=MSIL">
          <HintPath>..\packages\Bunifu.Licensing.6.0.9\lib\net48\Bunifu.Licensing.dll</HintPath>
        </Reference>
        <Reference Include="mscorlib" />
        <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
          <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
        </Reference>
        <Reference Include="System"/>
        <Reference Include="System.CodeDom, Version=10.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
          <HintPath>..\packages\System.CodeDom.10.0.0-preview.3.25171.5\lib\net462\System.CodeDom.dll</HintPath>
        </Reference>
        <Reference Include="System.ComponentModel.Composition" />
        <Reference Include="System.Core"/>
        <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
        </Reference>
        <Reference Include="System.Management" />
        <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
        </Reference>
        <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
        </Reference>
        <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
        </Reference>
        <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
        </Reference>
        <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
        </Reference>
        <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
          <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
        </Reference>
        <Reference Include="System.Xml.Linq"/>
        <Reference Include="System.Data.DataSetExtensions"/>
        <Reference Include="Microsoft.CSharp"/>
        <Reference Include="System.Data"/>
        <Reference Include="System.Deployment"/>
        <Reference Include="System.Drawing"/>
        <Reference Include="System.Windows.Forms"/>
        <Reference Include="System.Xml"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Include="AboutView.cs" />
        <Compile Include="AccountsView.cs" />
        <Compile Include="AccountsView.Designer.cs" />
        <Compile Include="ControlView.cs" />
        <Compile Include="MainForm.cs">
            <SubType>Form</SubType>
        </Compile>
        <Compile Include="MainForm.Designer.cs">
            <DependentUpon>MainForm.cs</DependentUpon>
        </Compile>
        <Compile Include="Program.cs"/>
        <Compile Include="Properties\AssemblyInfo.cs"/>
        <EmbeddedResource Include="AccountsView.resx">
          <DependentUpon>AccountsView.cs</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Include="MainForm.resx">
          <DependentUpon>MainForm.cs</DependentUpon>
        </EmbeddedResource>
        <EmbeddedResource Include="Properties\Resources.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>Resources.Designer.cs</LastGenOutput>
            <SubType>Designer</SubType>
        </EmbeddedResource>
        <Compile Include="Properties\Resources.Designer.cs">
            <AutoGen>True</AutoGen>
            <DependentUpon>Resources.resx</DependentUpon>
        </Compile>
        <None Include="packages.config" />
        <None Include="Properties\Settings.settings">
            <Generator>SettingsSingleFileGenerator</Generator>
            <LastGenOutput>Settings.Designer.cs</LastGenOutput>
        </None>
        <Compile Include="Properties\Settings.Designer.cs">
            <AutoGen>True</AutoGen>
            <DependentUpon>Settings.settings</DependentUpon>
            <DesignTimeSharedInput>True</DesignTimeSharedInput>
        </Compile>
    </ItemGroup>
    <ItemGroup>
        <None Include="App.config"/>
    </ItemGroup>
    <ItemGroup>
      <Content Include="AccountsView_Controls.txt" />
    </ItemGroup>
    <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets"/>
</Project>