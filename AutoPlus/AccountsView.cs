using System;
using System.Drawing;
using System.Windows.Forms;

namespace AutoPlus
{
    public partial class AccountsView : UserControl
    {
        public AccountsView()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            // Thêm dữ liệu mẫu vào DataGridView
            dataGridView1.Rows.Add("0", "s7ra1ndr0p311c", "V<PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("1", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("2", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("3", "<EMAIL>", "<PERSON><PERSON> trụ 7", "Không có", "");
            dataGridView1.Rows.Add("4", "<EMAIL>", "<PERSON><PERSON> trụ 7", "<PERSON>h<PERSON><PERSON> có", "");
            dataGridView1.Rows.Add("5", "***********", "Vũ trụ 7", "Không có", "");
            dataGridView1.Rows.Add("6", "***********", "Vũ trụ 7", "Không có", "");
        }
        
    }
}
